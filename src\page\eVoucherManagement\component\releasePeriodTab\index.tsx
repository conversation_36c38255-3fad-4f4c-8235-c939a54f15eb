import {
  Button,
  Checkbox,
  Col,
  DatePicker,
  Form,
  FormInstance,
  Input,
  Modal,
  notification,
  Row,
  Select,
  Spin,
  Switch,
  Table,
  TableColumnsType,
  TimePicker,
  Typography,
  Upload,
  UploadFile,
} from 'antd';

import dayjs from 'dayjs';
import React, { useCallback, useEffect } from 'react';
import { DeleteOutlined, PictureOutlined, PlusOutlined } from '@ant-design/icons';
import { v4 as uuidv4 } from 'uuid';
import { RcFile } from 'antd/lib/upload';
import { AxiosError } from 'axios';
import { IEVoucher, TBusinessPartner } from '../../../../types/eVoucher';
import { TAttachment } from '../../../../types/proposal';
import { FORMAT_DATE, OPTIONS_TYPE_E_VOUCHER } from '../../../../constants/common';
import { uploadImage } from '../../../../service/evoucher';
import { useEVoucherStore } from '../../stroreEVoucher';
import { handleKeyDownAmountInterger } from '../../utils';

interface CommonFormProps {
  data?: IEVoucher;
  form?: FormInstance;
  onFormInstanceChange?: (formInstance: FormInstance) => void;
  listPartner: TBusinessPartner[];
  actived: number;
  setListPartner: React.Dispatch<React.SetStateAction<TBusinessPartner[]>>;
  setActived: React.Dispatch<React.SetStateAction<number>>;
  avatarImage?: TAttachment;
  setAvatarImage?: React.Dispatch<React.SetStateAction<TAttachment | undefined>>;
  detailImage?: TAttachment;
  setDetailImage?: React.Dispatch<React.SetStateAction<TAttachment | undefined>>;
  setIsModified?: (value: boolean) => void;
}

const { Item } = Form;
const { Title, Text } = Typography;
const { Option } = Select;

const OPTIONS_TYPE_PERIOD_USE = [
  { label: 'Giờ', value: 'HOUR' },
  { label: 'Ngày', value: 'DAY' },
  { label: 'Tuần', value: 'WEEK' },
  { label: 'Tháng', value: 'MONTH' },
];

const ReleasePeriod: React.FC<CommonFormProps> = (props: CommonFormProps) => {
  const { listPartner, actived, avatarImage, detailImage, setListPartner, setActived, setAvatarImage, setDetailImage } =
    props;

  const { initialValue, disabled: disableField, setIsModified } = useEVoucherStore();
  const form = Form.useFormInstance(); // lấy form hiện tại từ context
  const [checkedActiveTime, setCheckedActiveTime] = React.useState(initialValue?.activeTime !== undefined);
  const [uploadingAvt, setUploadingAvt] = React.useState(false);
  const [uploadingDetail, setUploadingDetail] = React.useState(false);
  const [previewImgUploadOpen, setPreviewImgUploadOpen] = React.useState(false);
  const [previewImageUpload, setPreviewImageUpload] = React.useState('');

  const typeEVoucher = Form.useWatch('typeEVoucher', form);
  const promotionType = Form.useWatch('promotionType', form);

  const handlePreview = async (file: UploadFile) => {
    setPreviewImageUpload(file?.url || '');
    setPreviewImgUploadOpen(true);
  };

  const handelChangeStatus = React.useCallback(
    (checked: boolean) => {
      setActived(checked ? 1 : 2);
    },
    [setActived],
  );

  const handleAddPartnerRecord = React.useCallback(() => {
    const partner = { id: uuidv4() };
    setListPartner(prev => {
      const updatedListPartners = [...prev, partner];
      form?.setFieldValue('businessPartner', updatedListPartners);
      return updatedListPartners;
    });
  }, [form, setListPartner]);

  const handleDeletePartnerRecord = React.useCallback(
    (record: TBusinessPartner) => {
      const newRates = listPartner;
      const findIndex = newRates?.findIndex(o => o?.id === record?.id);
      if (findIndex !== undefined && findIndex !== null) {
        setListPartner(prev => {
          const updatedListPartners = prev?.slice(0, findIndex)?.concat(prev?.slice(findIndex + 1, prev?.length))
            ? prev?.slice(0, findIndex)?.concat(prev?.slice(findIndex + 1, prev?.length))
            : [];
          form?.setFieldValue('businessPartner', updatedListPartners);
          return updatedListPartners;
        });
      }
    },
    [form, listPartner, setListPartner],
  );

  const handleChangeField = React.useCallback(
    (value: string, record: TBusinessPartner, fieldName: string) => {
      if (!record?.id) return;

      setListPartner(prev => {
        const index = prev.findIndex(item => item?.id === record.id);
        if (index === -1) return prev; // Không tìm thấy => không thay đổi

        const updated = [...prev];
        updated[index] = { ...updated[index], [fieldName]: value };
        form?.setFieldValue('businessPartner', updated);
        return updated;
      });
    },
    [form, setListPartner],
  );

  const handleChangePromotionValue = React.useCallback(
    (value: string) => {
      switch (value) {
        case 'discount':
          form.setFieldValue('promotionType', 'percent');
          break;
        default:
          form.setFieldValue('promotionType', '');
      }
    },
    [form],
  );

  useEffect(() => {
    form?.setFieldsValue({ avatarImage: avatarImage });
    form?.setFieldsValue({ detailImage: detailImage });
  }, [avatarImage, detailImage, form]); // Cập nhật Form khi files thay đổi

  const handleCheckedActiveTime = useCallback(
    (checked: boolean) => {
      form?.setFieldValue(['activeTime', 'type'], 'DAY');
      setCheckedActiveTime(checked);
    },
    [form],
  );

  const columns: TableColumnsType = [
    {
      title: 'STT',
      dataIndex: 'index',
      key: 'index',
      width: 80,
      fixed: 'left',
      align: 'center',
      render: (_value, _record, index) => index + 1,
    },
    {
      title: 'Tên đối tác',
      dataIndex: 'name',
      key: 'name',
      render: (value: string, record: TBusinessPartner, index: number) => (
        <Form.Item
          name={['businessPartner', index, 'name']}
          rules={[
            {
              required: true,
              validator: (_, value) => {
                if (!value || value.trim() === '') {
                  return Promise.reject(new Error('Vui lòng nhập tên đối tác'));
                }
                return Promise.resolve();
              },
            },
          ]}
        >
          <Input
            onChange={e => handleChangeField(e?.target?.value, record, 'name')}
            defaultValue={value}
            disabled={disableField}
          />
        </Form.Item>
      ),
    },
    {
      title: 'Số lượng',
      width: '100px',
      dataIndex: 'merchantLimitAmount',
      key: 'merchantLimitAmount',
      render: (value: string, record: TBusinessPartner, index: number) => (
        <Form.Item
          name={['businessPartner', index, 'merchantLimitAmount']}
          rules={[
            ({ getFieldValue }) => ({
              validator: async (_, value) => {
                const amount = getFieldValue('amount');
                const businessPartners = getFieldValue('businessPartner') || [];

                // Calculate the total merchantLimitAmount
                const totalMerchantLimit = businessPartners.reduce((sum: number, partner: TBusinessPartner) => {
                  return (
                    sum + (partner.merchantLimitAmount && partner !== record ? Number(partner.merchantLimitAmount) : 0)
                  );
                }, 0);

                if (amount && Number(totalMerchantLimit) + Number(value) > Number(amount)) {
                  return Promise.reject('Vui lòng nhập số lượng nhỏ hơn hoặc bằng số lượng phát hành');
                }
                if (!value || value.trim() === '') {
                  return Promise.reject(new Error('Vui lòng nhập số lượng'));
                }
                return Promise.resolve();
              },
              validateTrigger: ['onChange'],
            }),
          ]}
        >
          <Input
            onKeyDown={handleKeyDownAmountInterger}
            onChange={e => handleChangeField(e?.target?.value, record, 'merchantLimitAmount')}
            defaultValue={value}
            maxLength={13}
            max={form?.getFieldValue('amount')}
            disabled={disableField}
          />
        </Form.Item>
      ),
    },
    {
      dataIndex: 'action',
      align: 'center',
      width: '80px',
      render: (_value: number, record: TBusinessPartner) => {
        return (
          <Text
            disabled={disableField}
            style={{ color: '#1677FF', cursor: 'pointer' }}
            onClick={() => handleDeletePartnerRecord(record)}
          >
            Xóa
          </Text>
        );
      },
    },
  ];

  return (
    <Row gutter={{ md: 24, lg: 40, xl: 80, xxl: 126 }}>
      <Col xs={24} md={12}>
        <Row gutter={{ md: 24, lg: 40 }}>
          <Col xs={24} md={24}>
            <Item
              label="Tên chương trình"
              name="nameOfHandover"
              required
              rules={[{ required: true, message: 'Vui lòng nhập tên chương trình' }]}
            >
              <Input
                placeholder="Nhập tên chương trình"
                onInput={(e: React.ChangeEvent<HTMLInputElement>) => {
                  e.target.value = e.target.value.toUpperCase(); // Chuyển đổi trực tiếp
                }}
                disabled={disableField}
                maxLength={100}
              />
            </Item>
          </Col>
          <Col xs={24} md={12}>
            <Item
              label="Mã E-voucher"
              name="prefixCode"
              required
              rules={[{ required: true, message: 'Vui lòng nhập mã e-voucher' }]}
            >
              <Input placeholder="Nhập mã e-voucher" maxLength={15} disabled={disableField} />
            </Item>
          </Col>
          <Col xs={24} md={12}>
            <Item
              label="Số lượng"
              name="amount"
              required
              rules={[{ required: true, message: 'Vui lòng nhập số lượng' }]}
            >
              <Input
                onKeyDown={handleKeyDownAmountInterger}
                placeholder="Nhập số lượng"
                maxLength={13}
                disabled={disableField}
              />
            </Item>
          </Col>
          <Col xs={24} md={7}>
            <Item label="Trạng thái voucher" name="isActive"></Item>
          </Col>
          <Col xs={24} md={3}>
            <Switch defaultValue={true} value={actived === 1} onChange={handelChangeStatus} disabled={disableField} />
          </Col>
          <Col xs={24} md={24}>
            <Title level={5}>Thông tin e-voucher</Title>
          </Col>
          <Col xs={24} md={24}>
            <Item
              label="Tên voucher"
              name="title"
              required
              rules={[{ required: true, message: 'Vui lòng nhập tên voucher' }]}
            >
              <Input placeholder="Nhập tên voucher" maxLength={100} disabled={disableField} />
            </Item>
          </Col>
          <Col xs={24} md={24}>
            <Item label="Loại" name="typeEVoucher" required rules={[{ required: true, message: 'Vui lòng chọn loại' }]}>
              <Select
                placeholder="Chọn loại"
                allowClear
                options={OPTIONS_TYPE_E_VOUCHER}
                onChange={handleChangePromotionValue}
                disabled={disableField}
              />
            </Item>
          </Col>
          {typeEVoucher === 'discount' && (
            <>
              <Col xs={24} md={12}>
                <Form.Item label="Giá trị được giảm" required>
                  <Input.Group compact>
                    <Form.Item
                      name="promotionValue"
                      noStyle
                      rules={[{ required: true, message: 'Vui lòng nhập giá trị được giảm' }]}
                    >
                      <Input
                        onKeyDown={handleKeyDownAmountInterger}
                        style={{ width: '70%' }}
                        placeholder="Nhập giá trị"
                        min={1}
                        maxLength={13}
                        disabled={disableField}
                      />
                    </Form.Item>

                    <Form.Item name="promotionType" noStyle>
                      <Select style={{ width: '30%' }} defaultValue={'percent'} disabled={disableField}>
                        <Option value="percent">%</Option>
                        <Option value="currency">VND</Option>
                      </Select>
                    </Form.Item>
                  </Input.Group>
                </Form.Item>
              </Col>

              {promotionType === 'percent' && (
                <Col xs={24} md={12}>
                  <Item
                    label="Số tiền tối đa được giảm"
                    name="maxAmountReduced"
                    required
                    rules={[{ required: true, message: 'Vui lòng nhập số tiền tối đa được giảm' }]}
                  >
                    <Input onKeyDown={handleKeyDownAmountInterger} placeholder="Nhập số tiền" disabled={disableField} />
                  </Item>
                </Col>
              )}
            </>
          )}
          <Col xs={24} md={24}>
            <Item valuePropName="checked" className="">
              <Checkbox
                checked={checkedActiveTime}
                onChange={e => handleCheckedActiveTime(e?.target?.checked)}
                style={{ marginRight: '8px' }}
                disabled={disableField}
              >
                Hạn sử dụng sau khi kích hoạt
              </Checkbox>
            </Item>
          </Col>
          {checkedActiveTime && (
            <>
              <Col xs={24} md={8}>
                <Item
                  name={['activeTime', 'type']}
                  required
                  rules={[{ required: true, message: 'Vui lòng chọn loại' }]}
                >
                  <Select allowClear defaultValue={'DAY'} options={OPTIONS_TYPE_PERIOD_USE} disabled={disableField} />
                </Item>
              </Col>
              <Col xs={24} md={16}>
                <Item
                  name={['activeTime', 'activeTime']}
                  labelCol={{ span: 24 }}
                  wrapperCol={{ span: 24 }}
                  rules={[{ required: true, message: 'Vui lòng nhập hạn sử dụng' }]}
                >
                  <Input placeholder="Nhập hạn sử dụng" disabled={disableField} />
                </Item>
              </Col>
            </>
          )}
          <Col xs={24} md={8}>
            <Item
              name={['startTime']}
              label="Thời gian bắt đầu"
              labelCol={{ span: 24 }}
              wrapperCol={{ span: 24 }}
              rules={[{ required: true, message: 'Vui lòng chọn giờ' }]}
            >
              <TimePicker
                format="HH:mm"
                placeholder="Chọn giờ"
                defaultOpenValue={dayjs('00:00', 'HH:mm')}
                disabled={disableField}
              />
            </Item>
          </Col>
          <Col xs={24} md={16} style={{ alignContent: 'end' }}>
            <Item
              name={['startDate']}
              labelCol={{ span: 24 }}
              wrapperCol={{ span: 24 }}
              rules={[{ required: true, message: 'Vui lòng nhập ngày bắt đầu' }]}
            >
              <DatePicker
                format={FORMAT_DATE}
                placeholder={'Chọn ngày '}
                disabledDate={(current, { from }) => {
                  return !!(from && current && current.isSame(from, 'second'));
                }}
                disabled={disableField}
              />
            </Item>
          </Col>
          <Col xs={24} md={8}>
            <Item
              name={['endTime']}
              label="Thời gian kết thúc"
              labelCol={{ span: 24 }}
              wrapperCol={{ span: 24 }}
              rules={[{ required: true, message: 'Vui lòng chọn giờ' }]}
            >
              <TimePicker
                format="HH:mm"
                placeholder="Chọn giờ"
                defaultOpenValue={dayjs('00:00', 'HH:mm')}
                disabled={disableField}
              />
            </Item>
          </Col>
          <Col xs={24} md={16} style={{ alignContent: 'end' }}>
            <Item
              name={['endDate']}
              labelCol={{ span: 24 }}
              wrapperCol={{ span: 24 }}
              required
              rules={[
                ({ getFieldValue }) => ({
                  validator(_, value) {
                    const startDate = getFieldValue(['startDate']);
                    if (value) {
                      if (startDate > value) {
                        return Promise.reject(new Error('Vui lòng chọn ngày bắt đầu nhỏ hơn hoặc bằng ngày kết thúc'));
                      } else if (value <= dayjs())
                        return Promise.reject(new Error('Vui lòng chọn ngày kết thúc lớn hơn ngày hiện tại'));
                      return Promise.resolve();
                    }
                    return Promise.reject(new Error('Vui lòng nhập ngày kết thúc'));
                  },
                }),
              ]}
            >
              <DatePicker
                format={FORMAT_DATE}
                placeholder={'Chọn ngày '}
                disabledDate={(current, { from }) => {
                  return !!(from && current && current.isSame(from, 'second'));
                }}
                disabled={disableField}
              />
            </Item>
          </Col>
          <Col xs={24} md={12} style={{ marginBottom: '16px' }}>
            <Row gutter={{ md: 24, lg: 10 }}>
              <Col xs={24} md={8}>
                <Button icon={<PlusOutlined />} onClick={handleAddPartnerRecord} disabled={disableField}>
                  Thêm
                </Button>
              </Col>
              <Col xs={24} md={16}>
                <Button icon={<DeleteOutlined />} onClick={() => setListPartner([])} disabled={disableField}>
                  Xóa tất cả
                </Button>{' '}
              </Col>
            </Row>
          </Col>
          <Col xs={24} md={24} style={{ marginBottom: '16px' }}>
            <Table className="partner-table" dataSource={listPartner} columns={columns} pagination={false} />
          </Col>
          <Col xs={24} md={24}>
            <Item
              label="Mô tả chi tiết"
              name="description"
              required
              rules={[{ required: true, message: 'Vui lòng nhập mô tả' }]}
            >
              <Input.TextArea rows={4} placeholder="Nhập mô tả chi tiết" maxLength={500} disabled={disableField} />
            </Item>
          </Col>
          <Col xs={24} md={6}>
            <Item
              name="avatarImage"
              label="Ảnh đại diện"
              valuePropName="fileList"
              getValueFromEvent={e => (Array.isArray(e) ? e : e?.fileList)}
              required={true}
              rules={[{ required: true, message: 'Vui lòng nhập ảnh đại diện' }]}
            >
              <>
                <Upload
                  name="avatarImage"
                  listType="picture-card"
                  maxCount={1}
                  fileList={avatarImage ? [{ ...avatarImage, uid: avatarImage?.uid ?? '1' } as RcFile] : []}
                  onRemove={() => {
                    setAvatarImage && setAvatarImage(undefined); // or set to empty array if you're using multiple files
                    form.setFieldValue('avatarImage', undefined);
                  }}
                  onPreview={handlePreview}
                  beforeUpload={file => {
                    const allowedExtensions = ['png', 'jpeg', 'jpg'];
                    const fileExtension = file.name.split('.').pop()?.toLowerCase(); // Extract the file extension

                    const isValidType = fileExtension && allowedExtensions.includes(fileExtension);
                    const isLt5M = file.size / 1024 / 1024 < 5;

                    if (!isValidType) {
                      notification.error({ message: 'Định dạng logo không đúng' });
                    } else if (!isLt5M) {
                      notification.error({ message: 'Kích thước tối đa 5MB.' });
                    }
                    setIsModified && setIsModified(true);

                    return isValidType && isLt5M ? true : Upload.LIST_IGNORE;
                  }}
                  customRequest={async ({ file, onSuccess, onError }) => {
                    setUploadingAvt(true);
                    try {
                      const response = await uploadImage(file as RcFile, 'e-voucher');
                      const { data } = response.data;

                      setAvatarImage &&
                        setAvatarImage({
                          uid: '1',
                          name: data?.fileUrl,
                          url: `${import.meta.env.VITE_S3_IMAGE_URL}/${data?.fileUrl}`,
                        });

                      form.setFieldValue('avatarImage', `${import.meta.env.VITE_S3_IMAGE_URL}/${data?.fileUrl}`);

                      onSuccess && onSuccess('ok');
                    } catch (error: unknown) {
                      onError && onError(error as AxiosError);
                    } finally {
                      setUploadingAvt(false);
                    }
                  }}
                  disabled={disableField}
                >
                  {uploadingAvt ? (
                    <Spin spinning={uploadingAvt}>
                      <div>
                        <PictureOutlined />
                        <div style={{ marginTop: 8 }}>Uploading...</div>
                      </div>
                    </Spin>
                  ) : (
                    <div>
                      <PictureOutlined />
                      <div style={{ marginTop: 8 }}>Upload</div>
                    </div>
                  )}
                </Upload>
                <div style={{ fontSize: 12, color: 'rgba(0, 0, 0, 0.45)', marginTop: 4 }}>Tối đa 5 MB</div>
              </>
            </Item>
          </Col>
          <Col xs={24} md={6}>
            <Item
              name="detailImage"
              label="Ảnh chi tiết"
              valuePropName="fileList"
              getValueFromEvent={e => (Array.isArray(e) ? e : e?.fileList)}
              required={true}
              rules={[{ required: true, message: 'Vui lòng nhập ảnh chi tiết' }]}
            >
              <>
                <Upload
                  name="detailImage"
                  listType="picture-card"
                  maxCount={1}
                  fileList={detailImage ? [{ ...detailImage, uid: detailImage?.uid ?? '1' } as RcFile] : []}
                  onPreview={handlePreview}
                  onRemove={() => {
                    setDetailImage && setDetailImage(undefined); // or set to empty array if you're using multiple files
                    form.setFieldValue('detailImage', undefined);
                  }}
                  beforeUpload={file => {
                    const allowedExtensions = ['png', 'jpeg', 'jpg'];
                    const fileExtension = file.name.split('.').pop()?.toLowerCase(); // Extract the file extension

                    const isValidType = fileExtension && allowedExtensions.includes(fileExtension);
                    const isLt5M = file.size / 1024 / 1024 < 5;

                    if (!isValidType) {
                      notification.error({ message: 'Định dạng logo không đúng' });
                    } else if (!isLt5M) {
                      notification.error({ message: 'Kích thước tối đa 5MB.' });
                    }
                    setIsModified && setIsModified(true);

                    return isValidType && isLt5M ? true : Upload.LIST_IGNORE;
                  }}
                  customRequest={async ({ file, onSuccess, onError }) => {
                    setUploadingDetail(true);
                    try {
                      const response = await uploadImage(file as RcFile, 'e-voucher');
                      const { data } = response.data;

                      setDetailImage &&
                        setDetailImage({
                          uid: '1',
                          name: data?.fileUrl,
                          url: `${import.meta.env.VITE_S3_IMAGE_URL}/${data?.fileUrl}`,
                        });

                      form.setFieldValue('detailImage', `${import.meta.env.VITE_S3_IMAGE_URL}/${data?.fileUrl}`);

                      onSuccess && onSuccess('ok');
                    } catch (error: unknown) {
                      onError && onError(error as AxiosError);
                    } finally {
                      setUploadingDetail(false);
                    }
                  }}
                  disabled={disableField}
                >
                  {uploadingDetail ? (
                    <Spin spinning={uploadingDetail}>
                      <div>
                        <PictureOutlined />
                        <div style={{ marginTop: 8 }}>Uploading...</div>
                      </div>
                    </Spin>
                  ) : (
                    <div>
                      <PictureOutlined />
                      <div style={{ marginTop: 8 }}>Upload</div>
                    </div>
                  )}
                </Upload>
                <div style={{ fontSize: 12, color: 'rgba(0, 0, 0, 0.45)', marginTop: 4 }}>Tối đa 5 MB</div>
              </>
            </Item>
          </Col>
        </Row>
      </Col>
      <Modal
        className="modal-preview-img-upload"
        width={800}
        open={previewImgUploadOpen}
        footer={null}
        onCancel={() => setPreviewImgUploadOpen(false)}
      >
        <img alt="preview" style={{ width: '100%' }} src={previewImageUpload} />
      </Modal>
    </Row>
  );
};
export default ReleasePeriod;
