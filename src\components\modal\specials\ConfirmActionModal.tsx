import { MutationFunction, QueryKey } from '@tanstack/react-query';
import { Button, DatePicker, Form, Input, Modal, notification } from 'antd';
import { useForm, useWatch } from 'antd/es/form/Form';
import './styleModalDelete.scss';
import { useUpdateField } from '../../../hooks';
import { useQueryClient } from '@tanstack/react-query';
import { useNavigate } from 'react-router-dom';
import dayjs from 'dayjs';
import { FORMAT_DATE } from '../../../constants/common';
interface ConfirmActionModalProps {
  open: boolean;
  onCancel: () => void;
  title?: string;
  description?: string;
  keyOfListQuery?: QueryKey;
  keyOfDetailQuery?: QueryKey;
  apiQuery: MutationFunction | MutationFunction<unknown, unknown>;
  label?: string;
  path?: string;
  disable?: boolean;
  extraValues?: { [key: string]: unknown };
  maxLength?: number;
  fieldNameReason?: string;
  isTitlePlaceholder?: boolean;
  labelConfirm?: string;
  isUpdate?: boolean;
  isMessageSuccess?: string;
  defaultFilter?: Record<string, unknown>;
  labelCancel?: string;
  idDetail?: string;
  payload?: Record<string, unknown>;
  showReasonField?: boolean; // Thêm prop để kiểm soát hiển thị Form.Item
  url?: string;
  isCenter?: boolean;
  onError?: (error: unknown, data?: unknown) => void;
  isDate?: boolean;
}

const ConfirmActionModal: React.FC<ConfirmActionModalProps> = ({
  open,
  onCancel,
  title,
  description,
  keyOfListQuery,
  apiQuery,
  label,
  path,
  keyOfDetailQuery,
  disable = false,
  fieldNameReason = 'reasonDelete',
  labelConfirm = 'Xác nhận',
  maxLength,
  labelCancel,
  isTitlePlaceholder,
  idDetail,
  payload,
  showReasonField = false, // Mặc định không hiển thị trường reason
  url,
  isCenter = false,
  onError,
  isDate = false,
}) => {
  const queryClient = useQueryClient();
  const [form] = useForm();
  const navigate = useNavigate();
  const hasReason = useWatch(fieldNameReason, form);
  const api = useUpdateField({
    keyOfListQuery,
    keyOfDetailQuery,
    apiQuery,
    label,
    path,
    isMessageError: false,
    isMessageSuccess: false,
    isShowMessage: isDate && false,
  });

  const handleConfirm = async (values: { [key: string]: unknown }) => {
    // Chỉ lấy reason nếu showReasonField là true
    const reason = showReasonField ? (values[fieldNameReason] as string) : undefined;

    // Nếu showReasonField là true và reason không tồn tại, dừng xử lý
    if (showReasonField && !reason) return;

    // Tạo payload
    const finalPayload = payload
      ? { ...payload, ...(reason && { [fieldNameReason]: reason }) } // Kết hợp payload với reason nếu có
      : { id: idDetail, ...(reason && { [fieldNameReason]: reason }) }; // Payload mặc định nếu không truyền payload

    const res = await api.mutateAsync(finalPayload);
    if (res?.data?.statusCode === '0') {
      notification.success({ message: `${title} thành công` });

      // Làm mới query chi tiết
      if (keyOfDetailQuery) {
        queryClient.invalidateQueries({ queryKey: keyOfDetailQuery });
      }
      onCancel();
      form.resetFields();
      if (url) {
        navigate(url);
      }
    } else {
      // API call thành công nhưng response chứa mã lỗi
      if (onError) {
        onError(res, res?.data);
      }
    }
  };

  const handleCancel = () => {
    onCancel();
    form.resetFields();
  };

  return (
    <Modal
      className="modal-confirm-delete"
      open={open}
      title={title}
      centered
      closable={false}
      okText="Tạo"
      cancelText="Huỷ"
      destroyOnClose
      style={{ textAlign: isCenter ? 'center' : undefined }}
      footer={[
        <Button key="cancel" className="btn-cancel" type="text" onClick={handleCancel}>
          {labelCancel || 'Huỷ'}
        </Button>,
        <Button
          key="confirm"
          className="btn-confirm"
          type="primary"
          onClick={form.submit}
          loading={api.status === 'pending'}
          disabled={(showReasonField && !hasReason && disable) || (!showReasonField && disable)}
        >
          {labelConfirm || 'Xác nhận'}
        </Button>,
      ]}
    >
      <Form form={form} onFinish={handleConfirm}>
        <p className="description">{description}</p>
        {showReasonField &&
          (isDate ? (
            <Form.Item
              name={fieldNameReason}
              rules={[{ required: true, message: 'Vui lòng chọn thời gian kết thúc!' }]}
            >
              <DatePicker format={FORMAT_DATE} disabledDate={current => current && current < dayjs().startOf('day')} />
            </Form.Item>
          ) : (
            <Form.Item
              name={fieldNameReason}
              rules={[{ required: true, message: 'Vui lòng nhập lý do!', whitespace: true }]}
            >
              <Input placeholder={isTitlePlaceholder ? 'Nhập lý do' : `Nhập lý do ${title}`} maxLength={maxLength} />
            </Form.Item>
          ))}
      </Form>
    </Modal>
  );
};

export default ConfirmActionModal;
