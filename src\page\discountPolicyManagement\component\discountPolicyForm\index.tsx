import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import _ from 'lodash';
import {
  Form,
  Input,
  DatePicker,
  Switch,
  Button,
  Select,
  Row,
  Col,
  Typography,
  UploadFile,
  InputNumber,
  FormInstance,
  Spin,
  Modal,
} from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import UploadFileDiscountPolicy from '../uploadFileDiscountPolicy';
import { DiscountPolicy } from '../../../../types/discountPolicy';
import { FORMAT_DATE, FORMAT_DATE_API } from '../../../../constants/common';
import { getListProject } from '../../../../service/project';
import SingleSelectLazy from '../../../../components/select/singleSelectLazy';
import { handleKeyDownEnterNumber } from '../../../../utilities/regex';
import PolicyReportModal from '../../../../components/policyReportModal';
import { useNavigate, useParams } from 'react-router-dom';
import { DISCOUNT_POLICY } from '../../../../configs/path';
import { useProjectStore } from '../../store';
import { useFetch } from '../../../../hooks';
import { getDiscountPolicyProposalNumber } from '../../../../service/report';
import './styles.scss';

const { Title } = Typography;
const { Option } = Select;
const { RangePicker } = DatePicker;

interface ExtendedUploadFile extends UploadFile {
  key?: string;
  absoluteUrl?: string;
}

interface DiscountPolicyFormProps {
  form?: FormInstance;
  initialValues?: DiscountPolicy;
  onFinish?: (values: DiscountPolicy) => void;
  isUpdate?: boolean;
  resetUpload?: boolean;
  setResetUpload?: (value: boolean) => void;
  loading?: boolean;
  isRefresh?: () => void;
}

const DiscountPolicyForm: React.FC<DiscountPolicyFormProps> = ({
  form: parentForm,
  initialValues,
  onFinish,
  isUpdate = false,
  resetUpload,
  setResetUpload,
  loading,
  isRefresh,
}) => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const { projectId: projectIdStore, setProjectId } = useProjectStore();
  const rangePickerRef = useRef<React.ComponentRef<typeof DatePicker.RangePicker>>(null);
  const [dates, setDates] = useState<[Dayjs | null, Dayjs | null]>([null, null]);
  const [internalForm] = Form.useForm();
  const form = parentForm || internalForm;
  const [fileList, setFileList] = useState<ExtendedUploadFile[]>([]);
  const [discountType, setDiscountType] = useState<string>(initialValues?.typeDiscount || 'percent');
  const [openPolicyReportModal, setOpenPolicyReportModal] = useState<boolean>(false);
  const [isDraftButtonDisabled, setIsDraftButtonDisabled] = useState<boolean>(false);
  // Kiểm tra điều kiện đặc biệt cho APPROVED_INACTIVE với expiredDate đã hết hạn
  const isExpiredApprovedInactive = useMemo(() => {
    if (initialValues?.status === 'APPROVED_INACTIVE' && initialValues?.expiredDate) {
      const today = dayjs().startOf('day');
      const expiredDate = dayjs(initialValues.expiredDate).startOf('day');
      return expiredDate.isBefore(today);
    }
    return false;
  }, [initialValues?.status, initialValues?.expiredDate]);

  const isDisable =
    !isExpiredApprovedInactive &&
    (initialValues?.ticketStatus === 'OPENED' ||
      initialValues?.ticketStatus === 'PROCESSING' ||
      initialValues?.ticketStatus === 'COMPLETED' ||
      initialValues?.ticketStatus === 'CLOSE' ||
      initialValues?.ticketStatus === 'ADDITIONAL_REQUEST' ||
      initialValues?.ticketStatus === 'RECALLING');

  // Disable riêng cho RangePicker - cho phép chỉnh sửa khi là APPROVED_INACTIVE với expiredDate hết hạn
  const isDatePickerDisable = isDisable && !isExpiredApprovedInactive;

  // Disable riêng cho Switch active - cho phép chỉnh sửa khi là APPROVED_INACTIVE với expiredDate hết hạn
  const isSwitchDisable = isDisable && !isExpiredApprovedInactive;

  const { refetch } = useFetch<DiscountPolicy>({
    queryKeyArr: ['get-proposal-number'],
    api: async () => {
      const response = await getDiscountPolicyProposalNumber(id || '');
      return response;
    },
    enabled: false,
  });

  const handleOpenPolicyReportModal = async () => {
    try {
      const eappNumber = form.getFieldValue('eappNumber');
      if (eappNumber) {
        setOpenPolicyReportModal(true);
        return;
      }

      const resp = await refetch();
      const responseData = resp?.data?.data;
      if (responseData) {
        setOpenPolicyReportModal(true);
      }
    } catch (error) {
      console.log('Error fetching proposal number:', error);
    }
  };

  // Chuẩn bị initial values
  const initialFormValues = React.useMemo(() => {
    return initialValues
      ? {
          ...initialValues,
          approvedDate: initialValues.approvedDate ? dayjs(initialValues.approvedDate).format(FORMAT_DATE) : '',
          applicationPeriod: [
            initialValues.startDate ? dayjs(initialValues.startDate) : null,
            initialValues.expiredDate ? dayjs(initialValues.expiredDate) : null,
          ],
          active: initialValues.active ?? true,
          typeDiscount: initialValues.typeDiscount || 'percent',
        }
      : {
          active: true,
          typeDiscount: 'percent',
        };
  }, [initialValues]);

  // Reset file upload khi được yêu cầu từ parent
  useEffect(() => {
    if (resetUpload) {
      setFileList([]);
      setResetUpload?.(false);
    }
  }, [resetUpload, setResetUpload]);

  // Dữ liệu ban đầu khi có initialValues
  useEffect(() => {
    if (initialValues) {
      form.setFieldsValue(initialFormValues);
      setDiscountType(initialValues.typeDiscount || 'percent');
      if (initialValues.files) {
        setFileList(
          initialValues.files.map(file => ({
            uid: file.uid || '',
            name: file.name || '',
            url: `${file.url}` || '',
            status: 'done',
          })),
        );
      }
    } else {
      form.resetFields();
      form.setFieldsValue({ active: true, typeDiscount: 'percent' });
      setDiscountType('percent');
      setFileList([]);
    }
  }, [initialValues, form, initialFormValues]);

  const name = Form.useWatch('name', form);
  const typeDiscount = Form.useWatch('typeDiscount', form);
  const value = Form.useWatch('value', form);
  const typeRealEstate = Form.useWatch('typeRealEstate', form);
  const projectId = Form.useWatch(['project', 'id'], form);
  const applicationPeriod = Form.useWatch('applicationPeriod', form);
  const active = Form.useWatch('active', form);
  const description = Form.useWatch('description', form);

  useEffect(() => {
    if (initialValues) {
      const isNameChanged = name !== initialValues.name && name !== undefined;
      const isTypeDiscountChanged = typeDiscount !== initialValues.typeDiscount && typeDiscount !== undefined;
      const isValueChanged = value !== initialValues.value && value !== undefined;
      const isTypeRealEstateChanged = typeRealEstate !== initialValues.typeRealEstate && typeRealEstate !== undefined;
      const isProjectChanged = !projectId || (projectId !== initialValues.project?.id && projectId !== undefined);
      const isApplicationPeriodChanged =
        (applicationPeriod?.[0] && !dayjs(applicationPeriod[0]).isSame(dayjs(initialValues.startDate), 'day')) ||
        (applicationPeriod?.[1] && !dayjs(applicationPeriod[1]).isSame(dayjs(initialValues.expiredDate), 'day')) ||
        (!applicationPeriod?.[0] && initialValues.startDate) ||
        (!applicationPeriod?.[1] && initialValues.expiredDate);
      const isActiveChanged = active !== initialValues.active && active !== undefined;
      const isDescriptionChanged = description !== initialValues.description && description !== undefined;
      const isFilesChanged = !_.isEqual(
        fileList.map(file => ({ name: file.name, url: file.key || file.url })),
        initialValues.files?.map(file => ({ name: file.name, url: file.url })) || [],
      );

      setIsDraftButtonDisabled(
        isNameChanged ||
          isTypeDiscountChanged ||
          isValueChanged ||
          isTypeRealEstateChanged ||
          isProjectChanged ||
          isApplicationPeriodChanged ||
          isActiveChanged ||
          isDescriptionChanged ||
          isFilesChanged,
      );
    } else {
      setIsDraftButtonDisabled(
        !!name ||
          !!typeDiscount ||
          !!value ||
          !!typeRealEstate ||
          !!projectId ||
          (applicationPeriod && (applicationPeriod[0] || applicationPeriod[1])) ||
          (active !== undefined && active !== true) ||
          !!description ||
          (fileList && fileList.length > 0),
      );
    }
  }, [
    name,
    typeDiscount,
    value,
    typeRealEstate,
    projectId,
    applicationPeriod,
    initialValues,
    active,
    description,
    fileList,
  ]);
  const handleFinish = (values: DiscountPolicy) => {
    const { applicationPeriod, active, ...rest } = values;
    const [startDate, expiredDate] = applicationPeriod || [];

    // Kiểm tra nếu switch active được bật và expiredDate nhỏ hơn ngày hiện tại
    if (active && expiredDate) {
      const today = dayjs().startOf('day');
      const expiredDateFormatted = dayjs(expiredDate).startOf('day');

      // Trường hợp đặc biệt: APPROVED_INACTIVE với expiredDate đã hết hạn được phép lưu
      if (!isExpiredApprovedInactive && expiredDateFormatted.isBefore(today)) {
        // Hiển thị message thông báo lỗi
        form.setFields([
          {
            name: 'applicationPeriod',
            errors: ['Chính sách đã hết hạn. Vui lòng điều chỉnh ngày kết thúc!'],
          },
        ]);
        return;
      }
    }

    const payload: DiscountPolicy = {
      ...rest,
      active: active !== undefined ? active : true,
      startDate: startDate ? dayjs(startDate).format(FORMAT_DATE_API) : undefined,
      expiredDate: expiredDate ? dayjs(expiredDate).format(FORMAT_DATE_API) : null,
      files: fileList.map(file => ({
        uid: file.uid || '',
        name: file.name,
        url: file.url || '',
        absoluteUrl: file?.absoluteUrl || '',
        uploadName: file.name,
      })),
    };
    onFinish?.(payload);
    setIsDraftButtonDisabled(false);
  };

  const defaultProjectValue = useMemo(() => {
    if (initialValues?.project) {
      return {
        label: `${initialValues.project.code} - ${initialValues.project.name}` || '',
        value: initialValues?.project?.id || '',
      };
    }
    return undefined;
  }, [initialValues?.project]);

  const handleSelectProject = (value: DiscountPolicy) => {
    form.setFieldsValue({
      project: {
        id: value?.id,
        name: value?.name,
      },
    });
  };

  const handleDiscountTypeChange = (value: string) => {
    setDiscountType(value);
    form.setFieldsValue({ value: undefined });
  };

  const handleCalendarChange = useCallback(
    (value: [Dayjs | null, Dayjs | null] | null) => {
      if (!value) {
        if (dates[0] !== null || dates[1] !== null) {
          setDates([null, null]);
          form.setFieldsValue({ applicationPeriod: [null, null] });
        }
        return;
      }

      const newStart = value[0];
      const newEnd = value[1];

      if (
        (newStart?.isSame(dates[0], 'day') || (!newStart && !dates[0])) &&
        (newEnd?.isSame(dates[1], 'day') || (!newEnd && !dates[1]))
      ) {
        return;
      }

      setDates([newStart, newEnd]);
      form.setFieldsValue({ applicationPeriod: [newStart, newEnd] });

      // Clear error khi thay đổi ngày
      form.setFields([
        {
          name: 'applicationPeriod',
          errors: [],
        },
      ]);
    },
    [dates, form],
  );

  const handleChangeRangePicker = useCallback(
    (value: [Dayjs | null, Dayjs | null] | null) => {
      if (!value) {
        if (dates[0] !== null || dates[1] !== null) {
          setDates([null, null]);
          form.setFieldsValue({ applicationPeriod: [null, null] });
        }
        return;
      }

      const newStart = value[0];
      const newEnd = value[1];

      if (
        (newStart?.isSame(dates[0], 'day') || (!newStart && !dates[0])) &&
        (newEnd?.isSame(dates[1], 'day') || (!newEnd && !dates[1]))
      ) {
        return;
      }

      setDates([newStart, newEnd]);
      form.setFieldsValue({ applicationPeriod: [newStart, newEnd] });

      // Clear error khi thay đổi ngày
      form.setFields([
        {
          name: 'applicationPeriod',
          errors: [],
        },
      ]);
    },
    [dates, form],
  );

  const handleBlur = useCallback(
    (e: React.FocusEvent<HTMLInputElement>, index: 0 | 1) => {
      const value = e.target.value;
      if (!value && dates[index] !== null) {
        const newDates = [...dates] as [Dayjs | null, Dayjs | null];
        newDates[index] = null;
        setDates(newDates);
        form.setFieldsValue({ applicationPeriod: newDates });
      }
    },
    [dates, form],
  );

  useEffect(() => {
    const formDates = form.getFieldValue('applicationPeriod');
    if (formDates && (!formDates[0]?.isSame(dates[0], 'day') || !formDates[1]?.isSame(dates[1], 'day'))) {
      setDates([formDates[0] || null, formDates[1] || null]);
    }
  }, [form, initialValues]);

  useEffect(() => {
    const pickerNode = rangePickerRef.current?.nativeElement as HTMLElement | null;
    const inputs = pickerNode?.querySelectorAll<HTMLInputElement>('input');
    if (inputs?.length === 2) {
      const input0 = inputs[0];
      const input1 = inputs[1];

      const blurHandler0 = (e: React.FocusEvent<HTMLInputElement>) => handleBlur(e, 0);
      const blurHandler1 = (e: React.FocusEvent<HTMLInputElement>) => handleBlur(e, 1);

      input0.addEventListener('blur', blurHandler0 as unknown as EventListener);
      input1.addEventListener('blur', blurHandler1 as unknown as EventListener);

      return () => {
        input0.removeEventListener('blur', blurHandler0 as unknown as EventListener);
        input1.removeEventListener('blur', blurHandler1 as unknown as EventListener);
      };
    }
  }, [handleBlur]);

  const handleActiveChange = useCallback(
    (checked: boolean) => {
      // Kiểm tra nếu switch được bật và có expiredDate
      if (checked) {
        const applicationPeriod = form.getFieldValue('applicationPeriod');
        const expiredDate = applicationPeriod?.[1];

        if (expiredDate) {
          const today = dayjs().startOf('day');
          const expiredDateFormatted = dayjs(expiredDate).startOf('day');

          // Trường hợp đặc biệt: APPROVED_INACTIVE với expiredDate đã hết hạn được phép chỉnh sửa
          if (isExpiredApprovedInactive) {
            // Clear error và cho phép bật switch
            form.setFields([
              {
                name: 'applicationPeriod',
                errors: [],
              },
            ]);
            return;
          }

          if (expiredDateFormatted.isBefore(today)) {
            // Hiển thị message thông báo lỗi và không cho phép bật switch
            form.setFields([
              {
                name: 'applicationPeriod',
                errors: ['Chính sách đã hết hạn. Vui lòng điều chỉnh ngày kết thúc!'],
              },
            ]);
            // Không cho phép bật switch
            form.setFieldValue('active', false);
            return;
          }
        }
      }

      // Clear error nếu có
      form.setFields([
        {
          name: 'applicationPeriod',
          errors: [],
        },
      ]);
    },
    [form, isExpiredApprovedInactive],
  );

  const handleCancel = useCallback(() => {
    if (form.isFieldsTouched()) {
      Modal.confirm({
        title: 'Xác nhận hủy',
        content: 'Dữ liệu chưa được lưu, bạn có chắc chắn muốn thoát khỏi trang không?',
        cancelText: 'Quay lại',
        okText: 'Đồng ý',
        onOk: () => {
          navigate(DISCOUNT_POLICY);
        },
        okButtonProps: {
          type: 'default',
        },
        cancelButtonProps: {
          type: 'primary',
        },
      });
    } else {
      navigate(DISCOUNT_POLICY);
    }
  }, [form, navigate]);

  return (
    <Spin spinning={loading}>
      <Form
        form={form}
        name="discount-policy-form"
        onFinish={handleFinish}
        initialValues={initialFormValues}
        layout="vertical"
      >
        <Row gutter={[64, 24]}>
          <Col span={12}>
            <Title level={5} style={{ marginBottom: 16 }}>
              Thông tin chung
            </Title>
            <Row gutter={24}>
              <Col span={14}>
                <Form.Item label="Mã tờ trình" name="eappNumber">
                  {initialValues?.urlEapp ? (
                    <a href={initialValues?.urlEapp} target="_blank" rel="noopener noreferrer">
                      <Input
                        placeholder="Mã tờ trình EAPP"
                        readOnly
                        value={initialValues?.eappNumber}
                        style={{ cursor: 'pointer', color: '#1890ff', backgroundColor: 'rgba(0, 0, 0, 0.04)' }}
                      />
                    </a>
                  ) : (
                    <Input placeholder="Mã tờ trình EAPP" disabled />
                  )}
                </Form.Item>
              </Col>
              <Col span={10}>
                <Form.Item label="Ngày được duyệt" name="approvedDate">
                  <Input placeholder="Ngày CS được duyệt" disabled />
                </Form.Item>
              </Col>
              <Col span={14}>
                <Form.Item
                  label="Tên chính sách"
                  name="name"
                  rules={[
                    {
                      required: true,
                      validator: (_, value) => {
                        if (!value || value.trim() === '') {
                          return Promise.reject(new Error('Vui lòng nhập tên chính sách'));
                        }
                        return Promise.resolve();
                      },
                    },
                  ]}
                >
                  <Input
                    placeholder="Nhập tên chính sách"
                    maxLength={255}
                    onBlur={e => {
                      form.setFieldsValue({ name: e.target.value.trim() });
                    }}
                    disabled={isDisable}
                  />
                </Form.Item>
              </Col>
              <Col span={10}>
                <Form.Item label="Mã chính sách" name="code">
                  <Input disabled placeholder="Mã chính sách" />
                </Form.Item>
              </Col>
            </Row>

            <Form.Item
              label="Thời gian"
              name="applicationPeriod"
              required
              rules={[
                {
                  validator: async (_, value: [Dayjs | null, Dayjs | null] | null) => {
                    if (!value || (!value[0] && !value[1])) {
                      return Promise.reject(new Error('Vui lòng chọn thời gian'));
                    }
                    if (!value[0]) {
                      return Promise.reject(new Error('Vui lòng chọn ngày bắt đầu'));
                    }
                    return Promise.resolve();
                  },
                },
              ]}
            >
              <RangePicker
                ref={rangePickerRef}
                value={dates}
                onCalendarChange={handleCalendarChange}
                onChange={handleChangeRangePicker}
                placeholder={['Ngày bắt đầu', 'Ngày kết thúc']}
                format="DD/MM/YYYY"
                allowClear={true}
                disabled={isDatePickerDisable}
              />
            </Form.Item>

            <Form.Item
              label="Dự án áp dụng"
              name={['project', 'id']}
              rules={[{ required: true, message: 'Vui lòng chọn dự án' }]}
            >
              <SingleSelectLazy
                apiQuery={getListProject}
                queryKey={['get-project']}
                placeholder="Chọn dự án"
                keysLabel={['name']}
                handleSelect={handleSelectProject}
                defaultValues={defaultProjectValue}
                disabled={isDisable}
              />
            </Form.Item>

            <Form.Item layout="horizontal" label="Kích hoạt" name="active" valuePropName="checked">
              <Switch
                defaultChecked={true}
                style={{ marginLeft: 140 }}
                disabled={isSwitchDisable}
                onChange={handleActiveChange}
              />
            </Form.Item>

            <Form.Item
              label="Giá trị chiết khấu"
              name="value"
              rules={[{ required: true, message: 'Vui lòng nhập giá trị chiết khấu' }]}
            >
              <InputNumber
                maxLength={discountType === 'percent' ? 5 : 15}
                style={{ width: '100%' }}
                placeholder="Nhập giá trị chiết khấu"
                min={0}
                max={discountType === 'percent' ? 100 : undefined}
                step={discountType === 'percent' ? 0.01 : 1}
                precision={2}
                formatter={(value: number | string | undefined) =>
                  discountType === 'currency' && value !== undefined
                    ? `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
                    : `${value}`
                }
                parser={(value: string | undefined) =>
                  discountType === 'currency' && value ? parseFloat(value.replace(/,/g, '')) : value || ''
                }
                onKeyDown={handleKeyDownEnterNumber}
                addonAfter={
                  <Form.Item name="typeDiscount" noStyle>
                    <Select style={{ width: 91 }} onChange={handleDiscountTypeChange}>
                      <Option value="percent">%</Option>
                      <Option value="currency">VND</Option>
                    </Select>
                  </Form.Item>
                }
                disabled={isDisable}
              />
            </Form.Item>

            <Form.Item
              label="Giá trị được áp dụng chiết khấu"
              name="typeRealEstate"
              rules={[{ required: true, message: 'Vui lòng chọn giá trị được áp dụng chiết khấu' }]}
            >
              <Select placeholder="Chọn giá trị được áp dụng" style={{ width: '50%' }} disabled={isDisable}>
                <Option value="default">Giá bán</Option>
                <Option value="house">Giá nhà</Option>
                <Option value="land">Giá đất</Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Title level={5} style={{ marginBottom: 16 }}>
              Tài liệu liên quan
            </Title>
            <Form.Item label="Tải tệp định kèm" name="files">
              <UploadFileDiscountPolicy fileList={fileList} setFileList={setFileList} isDetail={isDisable} />
            </Form.Item>
            <Form.Item label="Ghi chú" name="description">
              <Input.TextArea rows={4} placeholder="Nhập ghi chú" maxLength={250} disabled={isDisable} />
            </Form.Item>
          </Col>
        </Row>

        <div className="create-footer">
          <div className="button-create">
            {isUpdate && (
              <>
                {(initialValues?.status === 'NEW' ||
                  initialValues?.status === 'RETURNED' ||
                  initialValues?.status === 'REJECTED' ||
                  initialValues?.status === 'CANCELLED') && (
                  <Button
                    onClick={() => {
                      const projectId = initialValues?.project?.id || '';
                      setProjectId(projectId);
                      handleOpenPolicyReportModal();
                    }}
                    style={{ marginRight: 12 }}
                    disabled={loading || isDraftButtonDisabled}
                  >
                    Soạn tờ trình
                  </Button>
                )}
                {initialValues?.status === 'APPROVED_INACTIVE' && (
                  <Button htmlType="button" onClick={handleCancel} style={{ marginRight: 12 }}>
                    Hủy
                  </Button>
                )}
              </>
            )}
            <Button type="primary" htmlType="submit" loading={loading}>
              Lưu
            </Button>
          </div>
        </div>
      </Form>
      {projectIdStore && isUpdate && (
        <>
          <PolicyReportModal
            isOpen={openPolicyReportModal}
            onClose={() => setOpenPolicyReportModal(false)}
            title="Tạo tờ trình thay đổi"
            reload={isRefresh}
            record={initialValues}
            typePolicy="discount"
            projectId={projectId}
            formId="991c4223-7fdf-4fc0-929e-ffe80ecea284"
            urlType="discountPolicy"
            policyName="chiết khấu"
          />
        </>
      )}
    </Spin>
  );
};

export default DiscountPolicyForm;
