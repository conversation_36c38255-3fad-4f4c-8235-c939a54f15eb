import React, { useState } from 'react';
import { TableColumnsType, Typography, Row, Button, Col } from 'antd';
import FilterSearch from './components/FilterSearch';
import { DiscountPolicy } from '../../../types/discountPolicy';
import { useFetch } from '../../../hooks';
import TableComponent from '../../../components/table';
import BreadCrumbComponent from '../../../components/breadCrumb';
import {
  deleteDiscountPolicy,
  getListDiscountPolicy,
  updateStatusDiscountPolicy,
} from '../../../service/discountPolicy';
import { EAPP_STATUS_COLOR, EAPP_STATUS_NAME, FORMAT_DATE, getPolicyStatus } from '../../../constants/common';
import dayjs from 'dayjs';
import DiscountPolicyCreate from '../discountPolicyCreate';
import ConfirmDeleteModal from '../../../components/modal/specials/ConfirmDeleteModal';
import { MutationFunction } from '@tanstack/react-query';
import { ActionsColumns } from '../../../components/table/components/ActionsColumns';
import { Link, useNavigate } from 'react-router-dom';
import { DISCOUNT_POLICY } from '../../../configs/path';
import ConfirmActionModal from '../../../components/modal/specials/ConfirmActionModal';

const { Text } = Typography;

const DiscountPolicyList: React.FC = () => {
  const navigate = useNavigate();
  const [isModalCreateInvestor, setDiscountPolicyCreate] = useState<boolean>(false);
  const [isOpenModalDelete, setIsOpenModalDelete] = useState<boolean>(false);

  const [isOpenModalExtend, setIsOpenModalExtend] = useState<boolean>(false);
  const [isOpenModalActive, setIsOpenModalActive] = useState<boolean>(false);
  const [currentDiscountPolicy, setCurrentDiscountPolicy] = useState<DiscountPolicy>();

  const {
    data: discountPolicy,
    isLoading,
    isPlaceholderData,
    isFetching,
  } = useFetch<DiscountPolicy[]>({
    queryKeyArrWithFilter: ['get-discount-policy'],
    api: getListDiscountPolicy,
  });
  const discountPolicys = discountPolicy?.data?.data?.rows || [];

  const columns: TableColumnsType<DiscountPolicy> = [
    {
      title: 'Mã tờ trình',
      dataIndex: 'eappNumber',
      key: 'eappNumber',
      render: (_: string, record: DiscountPolicy) => {
        return record?.urlEapp ? (
          <Link to={`${record?.urlEapp}`} target="_blank">
            {record?.eappNumber || ''}
          </Link>
        ) : (
          <Text>{record?.eappNumber || ''}</Text>
        );
      },
    },
    {
      title: 'Mã chính sách',
      dataIndex: 'code',
      key: 'code',
    },
    {
      title: 'Tên chính sách',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'Dự án',
      dataIndex: ['project', 'name'],
      key: 'project',
    },
    {
      title: 'Thời gian áp dụng',
      dataIndex: 'date',
      key: 'date',
      render: (_: string, record: DiscountPolicy) => (
        <>
          <Text>{dayjs(record?.startDate).format(FORMAT_DATE) || ''}</Text>
          {record?.expiredDate && (
            <>
              {' - '}
              <Text>{dayjs(record?.expiredDate).format(FORMAT_DATE) || ''}</Text>
            </>
          )}
        </>
      ),
    },
    {
      title: 'Trạng thái duyệt',
      dataIndex: 'ticketStatus',
      key: 'ticketStatus',
      align: 'center',
      render: (value: string) => {
        return <Text style={{ color: EAPP_STATUS_COLOR[value] }}>{EAPP_STATUS_NAME[value]}</Text>;
      },
    },
    {
      title: 'Trạng thái chính sách',
      dataIndex: 'status',
      key: 'status',
      align: 'center',
      render: (value: string) => {
        const statusObject = getPolicyStatus(value);
        return <Text style={{ color: statusObject.value }}>{statusObject.label} </Text>;
      },
    },
    {
      title: 'Hành động',
      dataIndex: 'action',
      key: 'action',
      align: 'center',
      width: 140,
      render: (_, record: DiscountPolicy) => {
        const handleDeleteDiscountPolicy = () => {
          setIsOpenModalDelete(!isOpenModalDelete);
          setCurrentDiscountPolicy(record);
        };
        const handleActiveAndDeactiveDiscountPolicy = () => {
          setIsOpenModalActive(!isOpenModalActive);
          setCurrentDiscountPolicy(record);
        };
        const isApproved = record.status === 'APPROVED_ACTIVE' || record.status === 'APPROVED_INACTIVE';
        return (
          <ActionsColumns
            moreActions={[
              {
                label: 'Xem chi tiết',
                key: 'detail',
                onClick: () => navigate(`${DISCOUNT_POLICY}/${record?.id}`),
              },
              isApproved
                ? {
                    label: record.status === 'APPROVED_ACTIVE' ? 'Vô hiệu hóa' : 'Kích hoạt',
                    key: 'toggleStatus',
                    onClick: handleActiveAndDeactiveDiscountPolicy,
                    disabled: record.status === 'NEW' && true,
                  }
                : null,
              {
                label: 'Xóa',
                key: 'delete',
                onClick: handleDeleteDiscountPolicy,
              },
            ].filter(action => action !== null)}
          />
        );
      },
    },
  ];

  return (
    <div className="discount-policy-list-page">
      <BreadCrumbComponent />
      <div className="header-content">
        <FilterSearch />
        <Row gutter={[16, 8]}>
          <Col>
            <Button type="primary" onClick={() => setDiscountPolicyCreate(true)}>
              Tạo mới
            </Button>
          </Col>
        </Row>
      </div>

      <TableComponent
        queryKeyArr={['get-discount-policy']}
        className="table-discount-policy"
        columns={columns}
        loading={isLoading || isPlaceholderData || isFetching}
        dataSource={discountPolicys}
        rowKey="id"
      />

      <DiscountPolicyCreate
        visible={isModalCreateInvestor}
        onClose={() => {
          setDiscountPolicyCreate(false);
        }}
      />

      <ConfirmDeleteModal
        label="chính sách chiết khấu"
        open={isOpenModalDelete}
        apiQuery={deleteDiscountPolicy as MutationFunction<unknown, unknown>}
        keyOfListQuery={['get-discount-policy']}
        onCancel={() => setIsOpenModalDelete(false)}
        idDetail={currentDiscountPolicy?.id}
        title="Xóa chính sách chiết khấu"
        description="Vui lòng nhập lý do muốn xoá chính sách chiết khấu này"
      />

      <ConfirmActionModal
        open={isOpenModalActive}
        apiQuery={updateStatusDiscountPolicy as MutationFunction<unknown, unknown>}
        keyOfListQuery={['get-discount-policy']}
        onCancel={() => setIsOpenModalActive(false)}
        title={currentDiscountPolicy?.status === 'APPROVED_ACTIVE' ? 'Vô hiệu hóa chính sách' : 'Kích hoạt chính sách'}
        description={
          currentDiscountPolicy?.status === 'APPROVED_ACTIVE'
            ? 'Bạn có muốn vô hiệu hóa chính sách này không?'
            : 'Bạn có muốn kích hoạt chính sách này không?'
        }
        isTitlePlaceholder
        labelCancel="Hủy"
        labelConfirm="Xác nhận"
        maxLength={255}
        payload={{ id: currentDiscountPolicy?.id, expiredDate: currentDiscountPolicy?.expiredDate }}
        isCenter={true}
        onError={(_, data) => {
          if ((data as any)?.statusCode !== '0') {
            setIsOpenModalActive(false);
            setIsOpenModalExtend(true);
          }
        }}
      />

      <ConfirmActionModal
        open={isOpenModalExtend}
        apiQuery={updateStatusDiscountPolicy as MutationFunction<unknown, unknown>}
        keyOfListQuery={['get-discount-policy']}
        onCancel={() => setIsOpenModalExtend(false)}
        title="Gia hạn chính sách"
        description="Vui lòng điều chỉnh mốc thời gian kết thúc chính sách"
        isTitlePlaceholder
        labelCancel="Hủy"
        labelConfirm="Xác nhận"
        maxLength={255}
        fieldNameReason="expiredDate"
        payload={{ id: currentDiscountPolicy?.id }}
        isDate
        showReasonField={true}
      />
    </div>
  );
};

export default DiscountPolicyList;
