.custom-table-room {
  display: flex;
  gap: 52px;
  .name-block {
    margin-bottom: 0;
    background-color: #00000005;
    padding: 8px 16px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .virtual-table-cell {
    box-sizing: border-box;
    padding: 0;
    border-top: 1px solid #f0f0f0b7;
    border-bottom: 1px solid #f0f0f0b7;
    border-right: 1px solid #f0f0f097;
    font-size: 12px;
    line-height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    word-break: break-word;
    text-align: center;
    &.header {
      background-color: #fafafa;
      font-weight: 400;
    }
  }

  .status-cell {
    display: flex;
    width: 100%;
    height: 100%;
    align-items: center;
    justify-content: center;
  }

  .status-lock {
    color: #f5222d;
    background-color: #fff1f0;
    border: 1px solid #ffa39e;
  }

  .status-close {
    color: #13c2c2;
    background-color: #e6fffb;
    border: 1px solid #87e8de;
  }

  .status-processing {
    color: #faad14;
    background-color: #fffbe6;
    border: 1px solid #ffe58f;
  }
  .status-confirm {
    color: #1677ff;
    background-color: #e6f4ff;
    border: 1px solid #91caff;
  }

  .status-coming {
    color: #fadb14;
    background-color: #feffe6;
    border: 1px solid #fffb8f;
  }

  .status-success {
    color: #52c41a;
    background-color: #f6ffed;
    border: 1px solid #b7eb8f;
  }

  .status-unsuccess {
    color: #fa8c16;
    background-color: #fff7e6;
    border: 1px solid #ffd591;
  }

  .status-lock-confirm {
    color: #722ed1;
    background-color: #f9f0ff;
    border: 1px solid #d3adf7;
  }

  .status-lock-confirm-lock {
    color: #fa541c;
    background-color: #fff2e8;
    border: 1px solid #ffbb96;
  }

  .status-cancel {
    color: #434343;
    background-color: #f5f5f5;
    border: 1px solid #bfbfbf;
  }
  .status-mconfirm {
    color: #2f54eb;
    background-color: #f0f5ff;
    border: 1px solid #adc6ff;
  }

  .status-moved {
    color: #eb2f96;
    background-color: #fff0f6;
    border: 1px solid #ffadd2;
  }

  .status-default {
    color: inherit;
    background-color: inherit;
    border: 1px solid inherit;
  }
}
