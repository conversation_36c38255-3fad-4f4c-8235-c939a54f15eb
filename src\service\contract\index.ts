import { deleteRequest, getBlobRequest, getRequest, postRequest, putRequest } from '..';
import { typeQueryVersionApi, urlDomainApi } from '../../configs/domainApi';
import { TDepositContractPayload } from '../../types/contract/depositContract';
import { TPurchaseContractPayload } from '../../types/contract/purchaseContract';

export const getListContract = async (params: unknown) => {
  return await getRequest(`${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v2}/primary-contract`, {
    ...(params as Record<string, unknown> | undefined),
  });
};

export const softDelete = async (payload: { [key: string]: unknown }) => {
  return await deleteRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v2}/primary-contract/${payload.id}`,
    payload as Record<string, unknown> | undefined,
  );
};

export const getDetailContract = async (params?: unknown) => {
  const { id, ...restParams } = params as Record<string, unknown>;
  const response = await getRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v2}/primary-contract/${id}`,
    restParams as Record<string, unknown> | undefined,
  );
  return response;
};

export const createDepositContract = async (data: TDepositContractPayload) => {
  return await postRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v2}/primary-contract`,
    data as Record<string, unknown>,
  );
};

export const createPurchaseContract = async (data: TPurchaseContractPayload) => {
  return await postRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v2}/primary-contract/purchase`,
    data as Record<string, unknown>,
  );
};

export const getListDepositContractSelect = async (params?: unknown) => {
  const response = await getRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v2}/primary-contract/getDepositContract`,
    params as Record<string, unknown> | undefined,
  );
  return response;
};

export const getDetailDepositContractSelect = async (params?: unknown) => {
  const { id, ...restParams } = params as Record<string, unknown>;
  const response = await getRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v2}/primary-contract/${id}`,
    restParams as Record<string, unknown> | undefined,
  );
  return response;
};

export const getListYCDCTicket = async (params?: unknown) => {
  const response = await getRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/primaryTransaction/ticket/depositContract`,
    params as Record<string, unknown> | undefined,
  );
  return response;
};

export const getDetailYCDCTicket = async (params?: unknown) => {
  const { id, ...restParams } = params as Record<string, unknown>;
  const response = await getRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/primaryTransaction/ticketDetail/${id}`,
    restParams as Record<string, unknown> | undefined,
  );
  return response;
};

export const getListSalesPolicy = async (params?: unknown) => {
  const response = await getRequest(
    `${urlDomainApi.msx_commission}/${typeQueryVersionApi.query}/sales-policy/drop-down`,
    params as Record<string, unknown> | undefined,
  );
  return response;
};

export const getListPaymentPolicy = async (params?: unknown) => {
  const response = await getRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v2}/policy/payment/getAllByQuery`,
    params as Record<string, unknown> | undefined,
  );
  return response;
};

export const getListDiscountPolicy = async (params?: unknown) => {
  const response = await getRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v2}/policy/discount/getAllByQuery`,
    params as Record<string, unknown> | undefined,
  );
  return response;
};

export const getListEmployeeByOrgchartId = async (params?: unknown) => {
  const { id, ...restParams } = params as Record<string, unknown>;
  const response = await getRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v2}/primary-contract/employee-f1/${id}`,
    restParams as Record<string, unknown> | undefined,
  );
  return response;
};

export const updateDepositContract = async (data: TDepositContractPayload) => {
  return await putRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v2}/primary-contract`,
    data as Record<string, unknown>,
  );
};

export const updatePurchaseContract = async (data: TPurchaseContractPayload) => {
  return await putRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v2}/primary-contract`,
    data as Record<string, unknown>,
  );
};

export const getListCustomer = async (params?: unknown) => {
  const response = await getRequest(
    `${urlDomainApi.msx_customer}/${typeQueryVersionApi.query}/customer`,
    params as Record<string, unknown> | undefined,
  );
  return response;
};

export const getDetailCustomer = async (params?: unknown) => {
  const { id, ...restParams } = params as Record<string, unknown>;
  const response = await getRequest(
    `${urlDomainApi.msx_customer}/${typeQueryVersionApi.query}/customer/${id}`,
    restParams as Record<string, unknown> | undefined,
  );
  return response;
};

export const requestApproveContract = async (data: { id: string; status: string }) => {
  return await postRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v2}/primary-contract/requestApproveContract`,
    data as Record<string, unknown>,
  );
};

export const approveContract = async (data: { id: string; status: string }) => {
  return await postRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v2}/primary-contract/approveContract`,
    data as Record<string, unknown>,
  );
};

export const printContract = async (params?: unknown) => {
  const { id, contractName, ...restParams } = params as Record<string, unknown>;

  const response = await getBlobRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v2}/primary-contract/download/${id}`,
    restParams,
  );

  // Tạo blob URL từ file Word
  const fileBlob = new Blob([response.data], {
    type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  });

  const fileURL = window.URL.createObjectURL(fileBlob);

  const safeFileName = (name: string) => {
    return name.replace(/[^a-zA-Z0-9-_ \u00C0-\u1EF9]/g, '_');
  };

  const fileName = `${safeFileName((contractName as string) || 'hop-dong')}.docx`;

  const link = document.createElement('a');
  link.href = fileURL;
  link.download = fileName;
  document.body.appendChild(link);
  link.click();
  link.remove();

  window.URL.revokeObjectURL(fileURL);

  return response;
};

export const updateManyPrimaryContract = async (params?: unknown) => {
  return await postRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v2}/primary-contract/updateManyPrimaryContract`,
    params as Record<string, unknown>,
  );
};

// export const getDetailDepositContract = async (params: unknown) => {
//   const { id, restParams } = params as Record<string, unknown>;
//   return await getRequest(
//     `${urlDomainApi.msx_property}/${typeQueryVersionApi.query}/contract/${id}`,
//     restParams as Record<string, unknown>,
//   );
// };

export const getListInterestCalculations = async (params: unknown) => {
  const { id } = params as Record<string, unknown>;
  return await getRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v2}/primary-contract/${id}/list-interest-calculations`,
    {
      ...(params as Record<string, unknown> | undefined),
    },
  );
};

export const getDetailInterestCalculation = async (params?: unknown) => {
  const { id, idContract } = params as Record<string, unknown>;

  const response = await getRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v2}/primary-contract/interest/${idContract}?id=${id}`,
  );
  return response;
};

export const approveInterestCalculation = async (params?: unknown) => {
  return await putRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v2}/primary-contract/approveInterestCalculation`,
    params as Record<string, unknown>,
  );
};

export const getListBusinessArea = async (params?: unknown) => {
  const response = await getRequest(
    `${urlDomainApi.msx_masterdata_producer}/${typeQueryVersionApi.api_v1}/business-area/get-all`,
    params as Record<string, unknown> | undefined,
  );
  return response;
};

export const getListDistributionChannel = async (params?: unknown) => {
  const response = await getRequest(
    `${urlDomainApi.msx_masterdata_producer}/${typeQueryVersionApi.api_v1}/distribution-channel/get-all`,
    params as Record<string, unknown> | undefined,
  );
  return response;
};

export const getDivision = async (params?: unknown) => {
  const response = await getRequest(
    `${urlDomainApi.msx_masterdata_producer}/${typeQueryVersionApi.api_v1}/division/get-all`,
    params as Record<string, unknown> | undefined,
  );
  return response;
};

export const sendSAP = async (data: { id: string }) => {
  return await postRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v2}/primary-contract/send-sap`,
    data as Record<string, unknown>,
  );
};
