import { useMemo, useState } from 'react';
import TableComponent from '../../../../components/table';
import { useFetch } from '../../../../hooks';
import { softDelete, getListContract } from '../../../../service/contract';
import { TListTransferContract } from '../../../../types/contract/transferContract';
import { columns } from './columns';
import { Button, Col, Flex, Row, TableColumnsType, Typography } from 'antd';
import BreadCrumbComponent from '../../../../components/breadCrumb';
import FilterTransferContract from './FilterTransferContract';
import ConfirmDeleteModal from '../../../../components/modal/specials/ConfirmDeleteModal';
// import CreateTransferContract from '../createTransferContract';
import './styles.scss';
import useFilter from '../../../../hooks/filter';
import { MutationFunction } from '@tanstack/react-query';
// import UpdateReleaseDateModal from '../components/ConfirmActionModal';

const TransferContract = () => {
  const [isOpenModalDelete, setIsOpenModalDelete] = useState<boolean>(false);
  const [currentRecord, setCurrentRecord] = useState<TListTransferContract>();
  const [filterParams, setFilterParams] = useState<Record<string, unknown>>({});
  const [filter] = useFilter();

  const combinedFilter = useMemo(
    () => ({
      ...filterParams,
      page: filter.page || '1',
      pageSize: filter.pageSize || '10',
      isTransferred: true,
      _fields: `_id,id,code,name,sapCode,primaryTransaction.project.name,primaryTransaction.propertyUnit.salesProgram.name,primaryTransaction.propertyUnit.code,primaryTransaction.escrowTicketCode,primaryTransaction.customer.personalInfo.name,createdDate,type,liquidation.code,status,modifiedBy,createdBy,reason,deposit,isTransferred`,
    }),
    [filterParams, filter.page, filter.pageSize],
  );
  const { data, isFetching } = useFetch<TListTransferContract[]>({
    queryKeyArrWithFilter: ['list-transfer-contract', combinedFilter],
    defaultFilter: combinedFilter,
    api: getListContract,
    moreParams: combinedFilter,
  });

  const handleFilterChange = (newFilterParams: Record<string, unknown>) => {
    setFilterParams(newFilterParams);
  };

  const dataTransferContract = data?.data?.data?.rows || [];

  const actionsColumns: TableColumnsType<TListTransferContract> = useMemo(() => {
    return [
      ...columns,
      {
        title: '',
        dataIndex: 'action',
        key: 'action',
        align: 'center',
        width: '100px',
        render: (_, record) => {
          const handleDeleteTransferContract = (): void => {
            setIsOpenModalDelete(true);
            setCurrentRecord(record);
          };
          if (record.status === 'init') {
            return (
              <Typography.Link
                style={{ color: '#FF3B30' }}
                onClick={() => {
                  handleDeleteTransferContract();
                }}
              >
                Xoá
              </Typography.Link>
            );
          }
        },
      },
    ];
  }, []);

  return (
    <div className="wrapper-list-transfer-contract">
      <BreadCrumbComponent />
      <div style={{ marginBottom: 16 }}>
        <Flex gap={17} justify="space-between">
          <FilterTransferContract onFilterChange={handleFilterChange} />

          <Row gutter={[16, 16]}>
            <Col>
              <Button onClick={() => {}}>Tải về</Button>
            </Col>
          </Row>
        </Flex>
      </div>
      <TableComponent
        queryKeyArr={['list-transfer-contract', combinedFilter]}
        loading={isFetching}
        className="table-transfer-contract"
        columns={actionsColumns}
        dataSource={dataTransferContract || []}
        rowKey={'id'}
        isPagination={true}
        defaultFilter={combinedFilter}
      />
      <ConfirmDeleteModal
        label="Hợp đồng chuyển nhượng"
        open={isOpenModalDelete}
        apiQuery={softDelete as MutationFunction<unknown, unknown>}
        keyOfListQuery={['list-transfer-contract']}
        onCancel={() => setIsOpenModalDelete(false)}
        idDetail={currentRecord?.id}
        title="Xoá hợp đồng chuyển nhượng"
        description="Vui lòng nhập lý do muốn xoá "
      />
    </div>
  );
};

export default TransferContract;
