import { Col, Form, Row, Select } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import { useEffect, useMemo, useState } from 'react';
import { useLocation } from 'react-router-dom';
import { DEFAULT_PARAMS, FORMAT_DATE_API, OPTIONS_TYPE_E_VOUCHER } from '../../../../../constants/common';
import DropdownFilterSearch from '../../../../../components/dropdown/dropdownFilterSearch';
import DatePickerFilter from '../../../../../components/datePicker/DatePickerFilter';
import { useEVoucherStore } from '../../../stroreEVoucher';
import useFilter from '../../../../../hooks/filter';

type FilterEVoucher = {
  startCreatedDate?: string | Dayjs;
  endCreatedDate?: string | Dayjs;
  typeEVoucher?: string;
};

const FilterEVoucher = () => {
  const { search } = useLocation();
  const [form] = Form.useForm();
  const params = useMemo(() => new URLSearchParams(search), [search]);
  const [, setFilter] = useFilter();
  const [initialValues, setInitialValues] = useState<FilterEVoucher>();
  const [isOpenFilter, setIsOpenFilter] = useState(false);

  const { setFilter: setFilterParam, getCurrentFilter } = useEVoucherStore();

  useEffect(() => {
    if (params) {
      const initialValue = {
        startCreatedDate: params.get('startCreatedDate') ? dayjs(params.get('startCreatedDate')) : undefined,
        endCreatedDate: params.get('endCreatedDate') ? dayjs(params.get('endCreatedDate')) : undefined,
      };
      setInitialValues(initialValue);
      form.setFieldsValue(initialValue);
    }
  }, [form, params]);

  const handleSubmitFilter = (values: FilterEVoucher) => {
    const newFilter: Record<string, unknown> = {
      startCreatedDate: values?.startCreatedDate ? dayjs(values?.startCreatedDate).format(FORMAT_DATE_API) : null,
      endCreatedDate: values?.endCreatedDate ? dayjs(values?.endCreatedDate).format(FORMAT_DATE_API) : null,
      typeEVoucher: values?.typeEVoucher ? values?.typeEVoucher : null,
    };
    setFilterParam({ ...getCurrentFilter(), ...newFilter } as Record<string, unknown>);

    setFilter({ ...DEFAULT_PARAMS }); // Reset filter to default params with current tab

    setIsOpenFilter(false);
  };

  const handleOpenChangeDropdown = (value: boolean) => {
    setIsOpenFilter(value);
  };

  const handleClearFilters = () => {
    setInitialValues(undefined);
    setFilterParam({ search: getCurrentFilter()?.search ?? null });
    setTimeout(() => {
      setFilter({ ...DEFAULT_PARAMS });
      setIsOpenFilter(false);
    }, 100);
  };

  const handleChangeSearch = (e: unknown) => {
    const searchTerm = typeof e === 'string' ? e : '';
    setFilterParam({ ...getCurrentFilter(), search: searchTerm } as Record<string, unknown>);
  };

  return (
    <>
      <DropdownFilterSearch
        rootClassName="wrapper-filter"
        submitFilter={handleSubmitFilter}
        placeholder="Tìm kiếm"
        showParams={false}
        handleOpenChange={handleOpenChangeDropdown}
        isOpenFilter={isOpenFilter}
        initialValues={initialValues}
        defaultValueSearch={params.get('search') || ''}
        keyInputSearch={`search`}
        form={form}
        onChangeSearch={handleChangeSearch}
        onClearFilters={handleClearFilters}
        extraFormItems={
          <>
            <Row gutter={16}>
              <Col span={24}>
                <Form.Item label="Loại" name="typeEVoucher">
                  <Select placeholder="Chọn loại" allowClear options={OPTIONS_TYPE_E_VOUCHER} />
                </Form.Item>
              </Col>
              <Col span={24}>
                <DatePickerFilter startDate="startCreatedDate" endDate="endCreatedDate" />
              </Col>
            </Row>
          </>
        }
      />
    </>
  );
};

export default FilterEVoucher;
